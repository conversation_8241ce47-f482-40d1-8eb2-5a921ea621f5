<?php

namespace App\Http\Controllers;

use App\Models\Fund;
use App\Models\PaymentMethod;
use App\Models\PlatformSetting;
use App\Models\ReferralBonus;
use App\Models\User;
use App\Models\UserStat;
use App\Services\PaymentMatchTimeoutService;
use App\Services\UserPrivilegeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class FundController extends Controller
{
    /**
     * Display a listing of funds.
     * Users see only their own funds, admins see all funds.
     */
    public function index(Request $request): JsonResponse
    {
        // Check if user is admin to determine query scope
        if (auth()->user()->isAdmin()) {
            // Admin can see all funds with user relationship
            $query = Fund::with(['user', 'paymentMethod', 'userWithdraw']);

            // Filter by user_id if provided (admin-specific filter)
            if ($request->has('user_id') && ! empty($request->user_id)) {
                $query->where('user_id', $request->user_id);
            }

            // Filter by user email if provided (admin-specific filter)
            if ($request->has('email') && ! empty($request->email)) {
                $query->whereHas('user', function ($q) use ($request) {
                    $q->where('email', 'like', '%' . $request->email . '%');
                });
            }
        } else {
            // Users only see their own funds
            $query = auth()->user()->funds()->with(['paymentMethod', 'userWithdraw']);
        }

        // Filter by status if provided
        if ($request->has('status') && in_array($request->status, ['pending', 'matched', 'completed', 'cancelled'])) {
            $query->where('status', $request->status);
        }

        // Filter by currency if provided
        if ($request->has('currency') && in_array($request->currency, ['fiat', 'crypto'])) {
            $query->where('currency', $request->currency);
        }

        // Handle sorting
        if ($request->has('sort_field') && $request->has('sort_direction')) {
            $sortField = $request->sort_field;
            $sortDirection = in_array($request->sort_direction, ['asc', 'desc']) ? $request->sort_direction : 'desc';

            // Only allow specific fields for sorting for security
            $allowedSortFields = ['amount', 'created_at', 'status', 'currency'];
            if (in_array($sortField, $allowedSortFields)) {
                $query->orderBy($sortField, $sortDirection);
            } else {
                // Default sorting if invalid field provided
                $query->orderBy('created_at', 'desc');
            }
        } else {
            // Default sorting by created_at in descending order (newest first)
            $query->orderBy('created_at', 'desc');
        }

        // Pagination
        $isAdmin = auth()->user()->isAdmin();
        $perPage = min($request->get('per_page', 15), $isAdmin ? 100 : 50);
        $paginatedFunds = $query->paginate($perPage);

        // Structure response with separate funds and pagination
        $response = [
            'funds' => $paginatedFunds->items(),
            'pagination' => [
                'current_page' => $paginatedFunds->currentPage(),
                'last_page' => $paginatedFunds->lastPage(),
                'per_page' => $paginatedFunds->perPage(),
                'total' => $paginatedFunds->total(),
                'from' => $paginatedFunds->firstItem(),
                'to' => $paginatedFunds->lastItem(),
            ],
        ];

        return $this->success($response, 'Funds retrieved successfully');
    }

    /**
     * Store a newly created fund in storage.
     */
    public function store(Request $request): JsonResponse
    {
        // Check user account status first, before any other validation
        $user = auth()->user();

        // Auto-reactivate if deactivation period has expired
        $user->autoReactivateIfExpired();

        if (! $user->is_active) {
            return $this->forbidden('Your account has been deactivated. Please contact support or wait for automatic reactivation.');
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
            'currency' => 'required|in:fiat,crypto',
            'payment_method_id' => 'required|uuid|exists:payment_methods,id',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        // Validate amount step (multiples)
        $fiatStep = 10; // NGN must be in multiples of 10
        $cryptoStep = 0.025; // SOL must be in multiples of 0.025

        $step = $request->currency === 'fiat' ? $fiatStep : $cryptoStep;

        // Handle floating point precision for crypto amounts
        if ($request->currency === 'crypto') {
            // Convert to the smallest unit to avoid floating point precision issues
            $amountInSmallestUnit = (int) ($request->amount * 1000); // Convert to millicents
            $stepInSmallestUnit = (int) ($cryptoStep * 1000);
            $isValidMultiple = $amountInSmallestUnit % $stepInSmallestUnit === 0;
        } else {
            $isValidMultiple = fmod($request->amount, $step) === 0.0;
        }

        if (! $isValidMultiple) {
            return $this->error(
                $request->currency === 'fiat'
                    ? 'Amount must be a multiple of NGN ' . number_format($fiatStep, 0)
                    : 'Amount must be a multiple of ' . number_format($cryptoStep, 3) . ' SOL',
                400
            );
        }

        // Get privilege service and calculate/update user privileges
        $privilegeService = new UserPrivilegeService;

        // Calculate and update user privileges before creating fund
        $privilegeService->calculateAndUpdatePrivileges($user);

        // Get amount limits based on currency and user privileges
        $minAmountSetting = $request->currency === 'fiat'
            ? 'minimum_fiat_fund_amount'
            : 'minimum_crypto_fund_amount';

        $minAmount = PlatformSetting::getSetting($minAmountSetting);
        $maxAmount = $privilegeService->getMaximumFundAmount($user, $request->currency);

        // Validate amount against limits
        if ($request->amount < $minAmount) {
            return $this->error('Amount is below the minimum allowed of ' .
                ($request->currency === 'fiat'
                    ? 'NGN ' . number_format($minAmount, 0)
                    : number_format($minAmount, 2) . ' SOL'), 400);
        }

        if ($request->amount > $maxAmount) {
            return $this->error('Amount exceeds the maximum allowed of ' .
                ($request->currency === 'fiat'
                    ? 'NGN ' . number_format($maxAmount, 0)
                    : number_format($maxAmount, 2) . ' SOL'), 400);
        }

        // Check if payment method belongs to the user
        $paymentMethod = PaymentMethod::find($request->payment_method_id);
        if (! $paymentMethod || $paymentMethod->user_id !== auth()->id()) {
            return $this->error('Invalid payment method', 400);
        }

        // Check if payment method type matches the currency
        if (($request->currency === 'fiat' && $paymentMethod->type !== 'bank') ||
            ($request->currency === 'crypto' && $paymentMethod->type !== 'crypto')) {
            return $this->error('Payment method type does not match the currency', 400);
        }

        // Get platform settings
        $platformFeePercentage = PlatformSetting::getSetting('platform_fee_percentage');
        $growthRate = $request->currency === 'fiat'
            ? PlatformSetting::getSetting('fiat_growth_rate')
            : PlatformSetting::getSetting('crypto_growth_rate');

        // Get maturity days based on user privileges
        $maturityDays = $privilegeService->getMaturityDays($user);

        $startDate = now();
        $maturityDate = $startDate->copy()->addDays($maturityDays);

        // Calculate growth amount
        $growthAmount = $request->amount * ($growthRate / 100);

        // Calculate referral bonus limit
        // Ensure that referral bonus + growth amount doesn't exceed 100% of fund amount
        $maxBonusAndGrowthLimit = $request->amount; // 100% of fund amount
        $referralBonusLimit = max(0, $maxBonusAndGrowthLimit - $growthAmount);

        try {
            DB::beginTransaction();

            // Find eligible matured fund without a next fund (to link to)
            $eligiblePrevFund = $this->findEligiblePreviousFund($user->id, $request->amount, $request->currency);

            // Create the fund
            $fund = Fund::create([
                'user_id' => $user->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
                'payment_method_id' => $request->payment_method_id,
                'platform_fee_percentage' => $platformFeePercentage,
                'start_date' => $startDate,
                'maturity_date' => $maturityDate,
                'maturity_days' => $maturityDays,
                'growth_percentage' => $growthRate,
                'growth_amount' => $growthAmount,
                'referral_bonus_limit' => $referralBonusLimit,
                'is_eligible_for_withdrawal' => false,
                'amount_matched' => 0,
                'status' => 'pending', // Will change to 'matched' when fully matched, then 'completed' after payment
                'prev_fund_id' => $eligiblePrevFund ? $eligiblePrevFund->id : null,
            ]);

            // If we found an eligible previous fund, update its next_fund_id
            if ($eligiblePrevFund) {
                $eligiblePrevFund->next_fund_id = $fund->id;
                $eligiblePrevFund->save();
            }

            // Update user stats
            $userStat = UserStat::firstOrCreate(['user_id' => $user->id], [
                'updated_at' => now(),
            ]);
            if ($request->currency === 'fiat') {
                $userStat->incrementTotalFiatFunded($request->amount);
            } else {
                $userStat->incrementTotalCryptoFunded($request->amount);
            }

            DB::commit();

            return $this->success($fund, 'Fund created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to create fund: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified fund.
     */
    public function show(string $id): JsonResponse
    {
        // First get the fund to access its user_id
        $fund = Fund::find($id);

        if (! $fund) {
            return $this->notFound('Fund not found');
        }

        // Now load relationships with proper filtering
        $fund->load([
            'user:id,full_name,email,phone',
            'paymentMethod',
            'paymentMatches.paymentMethod',
            'paymentMatches.disputes',
            'paymentMatches.fundUser:id,full_name,email,phone',
            'paymentMatches.withdrawUser:id,full_name,email,phone',
            'nextFund',
            'userWithdraw',
            'withdraws' => function ($query) use ($fund) {
                $query->whereDoesntHave('platformFee')
                    ->where('user_id', $fund->user_id);
            },
        ]);

        // Check if the fund belongs to the authenticated user
        if ($fund->user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to view this fund');
        }

        return $this->success($fund, 'Fund retrieved successfully');
    }

    /**
     * Cancel the specified fund.
     */
    public function cancel(string $id): JsonResponse
    {
        $fund = Fund::find($id);

        if (! $fund) {
            return $this->notFound('Fund not found');
        }

        // Check if the fund belongs to the authenticated user
        if ($fund->user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to cancel this fund');
        }

        // Check if the fund is in a cancellable state
        if ($fund->status !== 'pending') {
            return $this->error('Only pending funds can be cancelled', 400);
        }

        try {
            DB::beginTransaction();

            // Cancel the fund
            $fund->status = 'cancelled';
            $fund->save();

            // Update user stats
            $userStat = UserStat::where('user_id', $fund->user_id)->first();
            if ($userStat) {
                if ($fund->currency === 'fiat') {
                    $userStat->decrementTotalFiatFunded($fund->amount);
                } else {
                    $userStat->decrementTotalCryptoFunded($fund->amount);
                }
            }

            // Cancel any referral bonuses associated with this fund
            ReferralBonus::where('fund_id', $fund->id)->delete();

            DB::commit();

            return $this->success($fund, 'Fund cancelled successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to cancel fund: ' . $e->getMessage());
        }
    }

    /**
     * Get funds statistics.
     * Admins see platform-wide statistics, users see their own statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            // Check if user is admin to determine query scope
            if (auth()->user()->isAdmin()) {
                // Admin gets platform-wide statistics
                $query = Fund::query();

                // Allow admin to filter by user_id if provided
                if ($request->has('user_id') && ! empty($request->user_id)) {
                    $query->where('user_id', $request->user_id);
                }

                // Allow admin to filter by user email if provided
                if ($request->has('email') && ! empty($request->email)) {
                    $query->whereHas('user', function ($q) use ($request) {
                        $q->where('email', 'like', '%' . $request->email . '%');
                    });
                }
            } else {
                // Users only see their own statistics
                $query = Fund::where('user_id', auth()->id());
            }

            // Optional date range filtering
            if ($request->has('date_from') && ! empty($request->date_from)) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && ! empty($request->date_to)) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $funds = $query->get();

            // Group funds by status for efficient calculations
            $fundsByStatus = $funds->groupBy('status');

            // Initialize status groups to ensure all statuses are represented
            $statuses = ['pending', 'matched', 'completed', 'cancelled'];
            foreach ($statuses as $status) {
                if (! $fundsByStatus->has($status)) {
                    $fundsByStatus[$status] = collect();
                }
            }

            // Helper function to calculate improved status stats
            $calculateStatusStats = function ($statusFunds) {
                $fiatFunds = $statusFunds->filter(fn ($f) => $f->currency === 'fiat');
                $cryptoFunds = $statusFunds->filter(fn ($f) => $f->currency === 'crypto');

                return [
                    'total_count' => $statusFunds->count(),
                    'count' => [
                        'fiat' => $fiatFunds->count(),
                        'crypto' => $cryptoFunds->count(),
                    ],
                    'amount' => [
                        'fiat' => round($fiatFunds->sum('amount'), 2),
                        'crypto' => round($cryptoFunds->sum('amount'), 6),
                    ],
                ];
            };

            // Calculate overall statistics
            $overallStats = $calculateStatusStats($funds);

            $statistics = [
                'overview' => [
                    'total_count' => $overallStats['total_count'],
                    'count' => $overallStats['count'],
                    'amount' => $overallStats['amount'],
                ],
                'statuses' => [
                    'pending' => $calculateStatusStats($fundsByStatus['pending']),
                    'matched' => $calculateStatusStats($fundsByStatus['matched']),
                    'completed' => $calculateStatusStats($fundsByStatus['completed']),
                    'cancelled' => $calculateStatusStats($fundsByStatus['cancelled']),
                ],
            ];

            return $this->success($statistics, 'Fund statistics retrieved successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to retrieve fund statistics: ' . $e->getMessage());
        }
    }

    // ======================================================================
    // Private utility methods
    // ======================================================================

    /**
     * Find an eligible previous fund that can be linked to a new fund.
     */
    private function findEligiblePreviousFund(string $userId, float $newFundAmount, string $currency): ?Fund
    {
        // Find matured funds without a next fund, ordered by maturity date (oldest first)
        return Fund::where('user_id', $userId)
            ->where('currency', $currency)
            ->where('next_fund_id', null)
            ->where('status', '!=', 'cancelled')
            ->where('amount', '<=', $newFundAmount)
            ->where('maturity_date', '<=', now())
            ->orderBy('maturity_date', 'asc')
            ->first();
    }

    /**
     * Get payment match timeout information for a specific fund.
     */
    public function getTimeoutInfo(string $id): JsonResponse
    {
        try {
            // First check if fund exists and user has access
            $fund = Fund::find($id);

            if (!$fund) {
                return $this->notFound('Fund not found');
            }

            // Check if user owns this fund or is admin
            if (!auth()->user()->isAdmin() && $fund->user_id !== auth()->id()) {
                return $this->forbidden('Unauthorized access to fund');
            }

            $timeoutService = new PaymentMatchTimeoutService();
            $timeoutInfo = $timeoutService->getFundTimeoutInfo($id);

            return $this->success($timeoutInfo, 'Fund timeout information retrieved successfully');

        } catch (\Exception $e) {
            return $this->serverError('Failed to retrieve timeout information: ' . $e->getMessage());
        }
    }
}
