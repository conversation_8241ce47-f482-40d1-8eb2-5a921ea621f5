<?php

namespace App\Http\Controllers;

use App\Models\ConfirmationCode;
use App\Models\PaymentMethod;
use App\Services\PaystackService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PaymentMethodController extends Controller
{
    use ApiResponse;

    protected $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Display a listing of the user's payment methods.
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();

        // Admin requesting platform payment methods
        if ($user->isAdmin() && $request->has('only_admins_payment_methods') && $request->only_admins_payment_methods === 'true') {
            $paymentMethods = PaymentMethod::with('user')
                ->whereHas('user', function ($query) {
                    $query->where('role', 'admin');
                })
                ->get();
        }
        // Admin requesting specific user's payment methods
        elseif ($user->isAdmin() && $request->has('user_id')) {
            $paymentMethods = PaymentMethod::with('user')
                ->where('user_id', $request->user_id)
                ->get();
        }
        // Default: user's own payment methods (works for both admin and regular users)
        else {
            $paymentMethods = $user->paymentMethods;
        }

        return $this->success($paymentMethods, 'Payment methods retrieved successfully');
    }

    /**
     * Store a newly created payment method in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:bank,crypto',
            'bank_name' => 'required_if:type,bank|string|max:255|nullable',
            'account_number' => 'required_if:type,bank|string|max:255|nullable',
            'account_name' => 'required_if:type,bank|string|max:255|nullable',
            'wallet_address' => 'required_if:type,crypto|string|max:255|nullable',
            'crypto_network' => 'required_if:type,crypto|string|max:255|nullable',
            'confirmation_code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        if (! ConfirmationCode::verifyCode(auth()->id(), ConfirmationCode::ACTION_CREATE, $request->input('confirmation_code'), ConfirmationCode::CONTEXT_PAYMENT_METHOD)) {
            return $this->error('Invalid or expired confirmation code.', 400);
        }

        $paymentMethod = PaymentMethod::create([
            'user_id' => auth()->id(),
            'type' => $request->type,
            'bank_name' => $request->bank_name,
            'account_number' => $request->account_number,
            'account_name' => $request->account_name,
            'wallet_address' => $request->wallet_address,
            'crypto_network' => $request->crypto_network,
            'status' => 'active',
        ]);

        return $this->success($paymentMethod, 'Payment method created successfully', 201);
    }

    /**
     * Display the specified payment method.
     */
    public function show(string $id): JsonResponse
    {
        $paymentMethod = PaymentMethod::find($id);

        if (! $paymentMethod) {
            return $this->notFound('Payment method not found');
        }

        // Check if the payment method belongs to the authenticated user
        if ($paymentMethod->user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to view this payment method');
        }

        return $this->success($paymentMethod, 'Payment method retrieved successfully');
    }

    /**
     * Update the specified payment method in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'bank_name' => 'sometimes|string|max:255|nullable',
            'account_number' => 'sometimes|string|max:255|nullable',
            'account_name' => 'sometimes|string|max:255|nullable',
            'wallet_address' => 'sometimes|string|max:255|nullable',
            'crypto_network' => 'sometimes|string|max:255|nullable',
            'status' => 'sometimes|in:active,inactive',
            'confirmation_code' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        if (! ConfirmationCode::verifyCode(auth()->id(), ConfirmationCode::ACTION_UPDATE, $request->input('confirmation_code'), ConfirmationCode::CONTEXT_PAYMENT_METHOD)) {
            return $this->error('Invalid or expired confirmation code.', 400);
        }

        $paymentMethod = PaymentMethod::with('user')->find($id);

        if (! $paymentMethod) {
            return $this->notFound('Payment method not found');
        }

        // Check if the payment method belongs to the authenticated user
        if ($paymentMethod->user_id !== auth()->id()) {
            return $this->forbidden('You do not have permission to update this payment method');
        }

        // Additional check: Only the admin can edit admin payment methods for platform fees
        if ($this->isAdminFeePaymentMethod($paymentMethod) && ! auth()->user()->isAdmin()) {
            return $this->forbidden('Only the admin can manage platform fee payment methods');
        }

        // Update only the fields that are provided
        $paymentMethod->fill($request->only([
            'bank_name',
            'account_number',
            'account_name',
            'wallet_address',
            'crypto_network',
            'status',
        ]));

        $paymentMethod->save();

        return $this->success($paymentMethod, 'Payment method updated successfully');
    }

    /**
     * Remove the specified payment method from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $validator = Validator::make(request()->all(), [
            'confirmation_code' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        if (! ConfirmationCode::verifyCode(auth()->id(), ConfirmationCode::ACTION_DELETE, request()->input('confirmation_code'), ConfirmationCode::CONTEXT_PAYMENT_METHOD)) {
            return $this->error('Invalid or expired confirmation code.', 400);
        }

        $paymentMethod = PaymentMethod::with('user')->find($id);

        if (! $paymentMethod) {
            return $this->notFound('Payment method not found');
        }

        // Check if the payment method belongs to the authenticated user
        if ($paymentMethod->user_id !== auth()->id()) {
            return $this->forbidden('You do not have permission to delete this payment method');
        }

        // Additional check: Only the admin can delete admin payment methods for platform fees
        if ($this->isAdminFeePaymentMethod($paymentMethod) && ! auth()->user()->isAdmin()) {
            return $this->forbidden('Only the admin can manage platform fee payment methods');
        }

        // Check if the payment method is being used in any active funds or withdraws
        $activeMatches = $paymentMethod->paymentMatches()->whereIn('status', ['pending', 'paid'])->count();

        if ($activeMatches > 0) {
            return $this->error('This payment method is currently being used in active transactions and cannot be deleted', 400);
        }

        $paymentMethod->delete();

        return $this->success(null, 'Payment method deleted successfully');
    }

    /**
     * Get list of banks from payment processor.
     */
    public function getBanks(Request $request): JsonResponse
    {
        $country = $request->get('country', 'NG'); // Default to Nigeria

        $result = $this->paystackService->getBanks($country);

        if ($result['success']) {
            return $this->success($result['banks'], $result['message']);
        }

        return $this->error($result['message'], 400);
    }

    /**
     * Validate bank account details using payment processor.
     */
    public function validateBankAccount(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'account_number' => 'required|string|max:20',
            'bank_code' => 'required|string|numeric',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $result = $this->paystackService->validateBankAccount(
            $request->account_number,
            $request->bank_code
        );

        if ($result['success']) {
            return $this->success([
                'account_name' => $result['account_name'],
                'account_number' => $result['account_number'],
                'bank_code' => $result['bank_code'],
            ], $result['message']);
        }

        return $this->error($result['message'], 400);
    }



    /**
     * Check if the payment method belongs to the admin for platform fees.
     */
    private function isAdminFeePaymentMethod(PaymentMethod $paymentMethod): bool
    {
        return $paymentMethod->user->isAdmin();
    }
}
