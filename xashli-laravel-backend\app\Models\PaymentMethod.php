<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentMethod extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'bank_name',
        'account_number',
        'account_name',
        'wallet_address',
        'crypto_network',
        'bank_code',
        'status',
        'last_used_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'string',
        'type' => 'string',
        'last_used_at' => 'datetime',
    ];

    /**
     * Get the user that owns the payment method.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the payment matches that use this payment method.
     */
    public function paymentMatches(): HasMany
    {
        return $this->hasMany(PaymentMatch::class);
    }

    /**
     * Check if the payment method is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the payment method is for bank.
     */
    public function isBank(): bool
    {
        return $this->type === 'bank';
    }

    /**
     * Check if the payment method is for crypto.
     */
    public function isCrypto(): bool
    {
        return $this->type === 'crypto';
    }

    /**
     * Check if the payment method is for fiat currency.
     */
    public function isFiat(): bool
    {
        return $this->type === 'bank';
    }

    /**
     * Get the display name for the payment method.
     */
    public function getDisplayName(): string
    {
        if ($this->isBank()) {
            return "{$this->bank_name} - {$this->account_number}";
        }

        if ($this->isCrypto()) {
            return "Crypto Wallet ({$this->crypto_network})";
        }

        return 'Unknown Payment Method';
    }
}
