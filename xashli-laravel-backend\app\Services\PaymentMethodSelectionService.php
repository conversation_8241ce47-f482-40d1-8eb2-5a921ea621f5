<?php

namespace App\Services;

use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class PaymentMethodSelectionService
{
    /**
     * Select an admin payment method intelligently based on usage patterns.
     * Prioritizes methods with older last_used_at timestamps for load balancing.
     *
     * @param string $type 'bank' or 'crypto'
     * @return PaymentMethod|null
     */
    public function selectAdminPaymentMethod(string $type): ?PaymentMethod
    {
        // Get admin account
        $adminAccount = User::where('role', 'admin')->first();
        
        if (!$adminAccount) {
            Log::warning('No admin account found for payment method selection');
            return null;
        }

        // Get all active admin payment methods of the specified type
        $paymentMethods = $adminAccount->paymentMethods()
            ->where('type', $type)
            ->where('status', 'active')
            ->get();

        if ($paymentMethods->isEmpty()) {
            Log::warning("No active {$type} payment methods found for admin account {$adminAccount->id}");
            return null;
        }

        // If only one method, return it
        if ($paymentMethods->count() === 1) {
            return $paymentMethods->first();
        }

        // Intelligent selection: prioritize methods with older last_used_at timestamps
        // Methods never used (last_used_at = null) get highest priority
        $selectedMethod = $paymentMethods->sortBy(function ($method) {
            // Null last_used_at gets priority (treated as oldest)
            return $method->last_used_at ?? now()->subYears(10);
        })->first();

        Log::info("Selected payment method {$selectedMethod->id} of type {$type} for admin account");

        return $selectedMethod;
    }

    /**
     * Update the last_used_at timestamp for a payment method.
     *
     * @param PaymentMethod $paymentMethod
     * @return bool
     */
    public function markPaymentMethodAsUsed(PaymentMethod $paymentMethod): bool
    {
        try {
            $paymentMethod->last_used_at = now();
            $paymentMethod->save();

            Log::info("Updated last_used_at for payment method {$paymentMethod->id}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to update last_used_at for payment method {$paymentMethod->id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment method usage statistics for admin methods.
     *
     * @return array
     */
    public function getAdminPaymentMethodStats(): array
    {
        $adminAccount = User::where('role', 'admin')->first();
        
        if (!$adminAccount) {
            return [
                'total_methods' => 0,
                'bank_methods' => 0,
                'crypto_methods' => 0,
                'never_used' => 0,
                'usage_distribution' => []
            ];
        }

        $allMethods = $adminAccount->paymentMethods()->where('status', 'active')->get();
        $bankMethods = $allMethods->where('type', 'bank');
        $cryptoMethods = $allMethods->where('type', 'crypto');
        $neverUsed = $allMethods->whereNull('last_used_at');

        $usageDistribution = $allMethods->map(function ($method) {
            return [
                'id' => $method->id,
                'type' => $method->type,
                'display_name' => $method->getDisplayName(),
                'last_used_at' => $method->last_used_at?->toISOString(),
                'never_used' => is_null($method->last_used_at),
            ];
        })->toArray();

        return [
            'total_methods' => $allMethods->count(),
            'bank_methods' => $bankMethods->count(),
            'crypto_methods' => $cryptoMethods->count(),
            'never_used' => $neverUsed->count(),
            'usage_distribution' => $usageDistribution,
        ];
    }

    /**
     * Reset usage statistics for all admin payment methods.
     * Useful for testing or rebalancing.
     *
     * @return int Number of methods reset
     */
    public function resetAdminPaymentMethodUsage(): int
    {
        $adminAccount = User::where('role', 'admin')->first();
        
        if (!$adminAccount) {
            return 0;
        }

        $count = $adminAccount->paymentMethods()
            ->where('status', 'active')
            ->update(['last_used_at' => null]);

        Log::info("Reset usage statistics for {$count} admin payment methods");

        return $count;
    }
}
