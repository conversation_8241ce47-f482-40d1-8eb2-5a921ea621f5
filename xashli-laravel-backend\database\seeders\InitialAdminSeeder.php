<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\UserStat;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class InitialAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'id' => Str::uuid(),
            'full_name' => 'System Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('Admin12345'),
            'role' => 'admin',
            'is_active' => true,
            'referral_code' => strtoupper(Str::random(8)),
            'referee_count' => 0,
            'email_verified_at' => now(),
        ]);

        // Create admin stats for tracking payment matching activities
        UserStat::create([
            'user_id' => $admin->id,
        ]);

        // Create multiple bank payment methods for admin (for fiat platform fees)
        $bankPaymentMethods = [
            [
                'bank_name' => 'Kuda MFB',
                'account_number' => '**********',
                'account_name' => 'I<PERSON>chukwu Amadi',
            ],
            [
                'bank_name' => 'GTBank',
                'account_number' => '**********',
                'account_name' => 'Xashli Platform',
            ],
            [
                'bank_name' => 'Access Bank',
                'account_number' => '**********',
                'account_name' => 'Xashli Operations',
            ],
            [
                'bank_name' => 'Zenith Bank',
                'account_number' => '**********',
                'account_name' => 'Xashli Finance',
            ],
            [
                'bank_name' => 'First Bank',
                'account_number' => '**********',
                'account_name' => 'Xashli Holdings',
            ],
        ];

        foreach ($bankPaymentMethods as $bankMethod) {
            PaymentMethod::create([
                'id' => Str::uuid(),
                'user_id' => $admin->id,
                'type' => 'bank',
                'bank_name' => $bankMethod['bank_name'],
                'account_number' => $bankMethod['account_number'],
                'account_name' => $bankMethod['account_name'],
                'status' => 'active',
                'last_used_at' => null, // Will be set when first used
            ]);
        }

        // Create multiple crypto payment methods for admin (for crypto platform fees)
        $cryptoPaymentMethods = [
            [
                'wallet_address' => '2KFCH6QpLNsPVEZ5ftusVjNdDzRUc1GfMb4s7JyFmSvA',
                'crypto_network' => 'Solana',
            ],
            [
                'wallet_address' => '8FGH9QpMNsPVEZ5ftusVjNdDzRUc1GfMb4s7JyFmSvB',
                'crypto_network' => 'Solana',
            ],
            [
                'wallet_address' => '3IJK0QpONsPVEZ5ftusVjNdDzRUc1GfMb4s7JyFmSvC',
                'crypto_network' => 'Solana',
            ],
            [
                'wallet_address' => '7LMN1QpPNsPVEZ5ftusVjNdDzRUc1GfMb4s7JyFmSvD',
                'crypto_network' => 'Solana',
            ],
            [
                'wallet_address' => '9OPQ2QpQNsPVEZ5ftusVjNdDzRUc1GfMb4s7JyFmSvE',
                'crypto_network' => 'Solana',
            ],
        ];

        foreach ($cryptoPaymentMethods as $cryptoMethod) {
            PaymentMethod::create([
                'id' => Str::uuid(),
                'user_id' => $admin->id,
                'type' => 'crypto',
                'wallet_address' => $cryptoMethod['wallet_address'],
                'crypto_network' => $cryptoMethod['crypto_network'],
                'status' => 'active',
                'last_used_at' => null, // Will be set when first used
            ]);
        }
    }
}
