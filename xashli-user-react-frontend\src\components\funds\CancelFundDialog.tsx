import { AlertTriangle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Modal } from '../ui';
import { formatCurrencyAmount } from '../../utils/format';
import type { Fund } from '../../types';

interface CancelFundDialogProps {
  open: boolean;
  fund: Fund | null;
  onConfirm: () => void;
  onCancel: () => void;
}

export function CancelFundDialog({
  open,
  fund,
  onConfirm,
  onCancel,
}: CancelFundDialogProps) {
  if (!fund) return null;



  return (
    <Modal isOpen={open} onClose={onCancel} title='Cancel Fund'>
      <div className='flex items-center gap-4 mb-4'>
        <div className='w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center'>
          <AlertTriangle className='h-6 w-6 text-destructive' />
        </div>{' '}
        <div>
          <p className='text-foreground-secondary'>
            Are you sure you want to cancel this fund?
          </p>
        </div>
      </div>

      <div className='bg-background-secondary rounded-lg p-4 mb-6 border border-border'>
        {' '}
        <div className='grid grid-cols-2 gap-4 text-sm'>
          <div>
            <span className='text-foreground-secondary'>Amount</span>
            <p className='font-medium text-foreground'>
              {formatCurrencyAmount(fund.amount, fund.currency)}
            </p>
          </div>
          <div>
            <span className='text-foreground-secondary'>Currency</span>
            <p className='font-medium text-foreground'>
              {fund.currency === 'fiat' ? 'Fiat (NGN)' : 'Crypto (SOL)'}
            </p>
          </div>
          <div>
            <span className='text-foreground-secondary'>Status</span>
            <p className='font-medium text-foreground capitalize'>
              {fund.status}
            </p>
          </div>
          <div>
            <span className='text-foreground-secondary'>Expected Growth</span>
            <p className='font-medium text-success'>
              {formatCurrencyAmount(fund.growth_amount, fund.currency)}
            </p>
          </div>
        </div>
      </div>

      <div className='bg-warning/10 border border-warning/20 rounded-lg p-4 mb-6'>
        <div className='flex gap-3'>
          <AlertTriangle className='h-5 w-5 text-warning flex-shrink-0 mt-0.5' />
          <div className='text-sm'>
            <p className='font-medium text-foreground mb-1'>Important Notice</p>
            <p className='text-foreground-secondary'>
              Cancelling this fund will permanently remove it from your account.
              This action cannot be undone. You will lose any potential growth
              earnings.
            </p>
          </div>
        </div>
      </div>

      <div className='flex gap-3'>
        <Button variant='outline' onClick={onCancel} className='flex-1'>
          Keep Fund
        </Button>
        <Button variant='destructive' onClick={onConfirm} className='flex-1'>
          Cancel Fund
        </Button>
      </div>
    </Modal>
  );
}
