import { useState, useEffect } from 'react';
import { Clock, AlertTriangle, Timer, Zap } from 'lucide-react';
import moment from 'moment';
import { Badge } from './Badge';
import { fundService } from '../../services/fund';

interface PaymentMatchTimeoutCountdownProps {
  fundId: string;
  className?: string;
}

interface TimeoutData {
  has_pending_matches: boolean;
  timeout_hours: number;
  is_expired?: boolean;
  timeout_at?: string;
  timeout_at_formatted?: string;
  remaining_seconds?: number;
  remaining_hours?: number;
  earliest_match_created_at?: string;
  pending_matches_count?: number;
  message?: string;
}

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  total: number;
}

export function PaymentMatchTimeoutCountdown({ 
  fundId, 
  className = '' 
}: PaymentMatchTimeoutCountdownProps) {
  const [timeoutData, setTimeoutData] = useState<TimeoutData | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch timeout data
  const fetchTimeoutData = async () => {
    try {
      setError(null);
      const response = await fundService.getTimeoutInfo(fundId);
      
      if (response.status === 'success') {
        setTimeoutData(response.data);
      } else {
        setError(response.message || 'Failed to fetch timeout information');
      }
    } catch (err) {
      console.error('Error fetching timeout data:', err);
      setError('Failed to load timeout information');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate time remaining
  const calculateTimeRemaining = (targetTime: string): TimeRemaining => {
    const now = moment();
    const target = moment(targetTime);
    const diff = target.diff(now);

    if (diff <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 };
    }

    const duration = moment.duration(diff);
    return {
      days: Math.floor(duration.asDays()),
      hours: duration.hours(),
      minutes: duration.minutes(),
      seconds: duration.seconds(),
      total: diff,
    };
  };

  // Format countdown display
  const formatCountdown = (time: TimeRemaining): string => {
    if (time.total <= 0) return '00:00:00';

    if (time.days > 0) {
      return `${time.days}d ${time.hours.toString().padStart(2, '0')}:${time.minutes.toString().padStart(2, '0')}:${time.seconds.toString().padStart(2, '0')}`;
    }
    
    return `${time.hours.toString().padStart(2, '0')}:${time.minutes.toString().padStart(2, '0')}:${time.seconds.toString().padStart(2, '0')}`;
  };

  // Update countdown every second
  useEffect(() => {
    if (!timeoutData?.timeout_at || timeoutData.is_expired) return;

    const updateCountdown = () => {
      const remaining = calculateTimeRemaining(timeoutData.timeout_at!);
      setTimeRemaining(remaining);

      // If countdown reaches zero, refetch data to check current status
      if (remaining.total <= 0) {
        fetchTimeoutData();
      }
    };

    updateCountdown(); // Initial calculation
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [timeoutData?.timeout_at, timeoutData?.is_expired]);

  // Initial data fetch
  useEffect(() => {
    fetchTimeoutData();
  }, [fundId]);

  // Don't render if loading
  if (isLoading) {
    return null;
  }

  // Don't render if error or no pending matches
  if (error || !timeoutData?.has_pending_matches) {
    return null;
  }

  // Don't render if expired
  if (timeoutData.is_expired) {
    return null;
  }

  const isUrgent = timeRemaining && timeRemaining.total > 0 && timeRemaining.total <= 6 * 60 * 60 * 1000; // Less than 6 hours
  const isCritical = timeRemaining && timeRemaining.total > 0 && timeRemaining.total <= 2 * 60 * 60 * 1000; // Less than 2 hours

  return (
    <div className={`bg-background-secondary rounded-lg border border-border p-4 ${className}`}>
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Timer className={`h-4 w-4 ${isCritical ? 'text-red-500' : isUrgent ? 'text-orange-500' : 'text-yellow-500'}`} />
            <span className="font-medium text-foreground">Payment Deadline</span>
          </div>
          <Badge className={`${
            isCritical 
              ? 'bg-red-500/10 text-red-600 border-red-200' 
              : isUrgent 
                ? 'bg-orange-500/10 text-orange-600 border-orange-200'
                : 'bg-yellow-500/10 text-yellow-600 border-yellow-200'
          }`}>
            {timeoutData.pending_matches_count} Pending
          </Badge>
        </div>

        {/* Warning Message */}
        <div className={`rounded-lg p-3 border ${
          isCritical 
            ? 'bg-red-500/5 border-red-500/20' 
            : isUrgent 
              ? 'bg-orange-500/5 border-orange-500/20'
              : 'bg-yellow-500/5 border-yellow-500/20'
        }`}>
          <div className="flex items-start gap-2">
            <AlertTriangle className={`h-4 w-4 mt-0.5 flex-shrink-0 ${
              isCritical ? 'text-red-500' : isUrgent ? 'text-orange-500' : 'text-yellow-500'
            }`} />
            <div className="text-sm">
              <p className={`font-medium ${
                isCritical ? 'text-red-700' : isUrgent ? 'text-orange-700' : 'text-yellow-700'
              }`}>
                Complete payment matches to avoid account deactivation
              </p>
              <p className={`text-xs mt-1 ${
                isCritical ? 'text-red-600' : isUrgent ? 'text-orange-600' : 'text-yellow-600'
              }`}>
                Your account will be deactivated for 2 days if payments aren't completed in time
              </p>
            </div>
          </div>
        </div>

        {/* Countdown */}
        {timeRemaining && timeRemaining.total > 0 && (
          <div className={`rounded-lg p-3 border ${
            isCritical 
              ? 'bg-red-500/5 border-red-500/20' 
              : isUrgent 
                ? 'bg-orange-500/5 border-orange-500/20'
                : 'bg-yellow-500/5 border-yellow-500/20'
          }`}>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-foreground">Time Remaining:</span>
              <span className={`text-lg font-mono font-bold ${
                isCritical ? 'text-red-600' : isUrgent ? 'text-orange-600' : 'text-yellow-600'
              }`}>
                {formatCountdown(timeRemaining)}
              </span>
            </div>
          </div>
        )}

        {/* Deadline Info */}
        {timeoutData.timeout_at_formatted && (
          <div className="text-xs text-foreground-secondary">
            Deadline: {timeoutData.timeout_at_formatted}
          </div>
        )}
      </div>
    </div>
  );
}
