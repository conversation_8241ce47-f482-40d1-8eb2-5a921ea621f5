import { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  Clock,
  TrendingUp,
  Wallet,
  FileText,
  Upload,
  AlertCircle,
  CheckCircle,
  XCircle,
  ArrowDownToLine,
  X,
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { Badge } from '../../components/ui';
import { DeactivatedUserAlert } from '../../components/ui/DeactivatedUserAlert';
import { NextMatchingCountdown } from '../../components/ui/NextMatchingCountdown';
import { PaymentMatchTimeoutCountdown } from '../../components/ui/PaymentMatchTimeoutCountdown';
import { FundPaymentMatchCard } from '../../components/funds';
import { PaymentModal } from '../../components/funds/PaymentModal';
import { CancelFundDialog } from '../../components/funds/CancelFundDialog';
import { useFund } from '../../hooks/funds';
import { formatCurrencyAmount, formatDateTime } from '../../utils/format';
import { useAuth } from '../../contexts/AuthContext';
import { usePaystackPopup } from '../../hooks/funds/usePaystackPopup';
import { withdrawService } from '../../services/withdraw';
import { paymentMatchService } from '../../services/paymentMatch';
import type { PaymentMatch } from '../../types/paymentMatch';
import { toNumber } from '../../utils/convert';

export function FundDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const { fund, loading, actionLoading, cancelFund, refreshFund } = useFund(
    id!
  );



  // Paystack popup hook
  const { initiatePayment: initiatePaystackPayment } = usePaystackPopup({
    onSuccess: refreshFund,
  });

  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);

  // Payment match related state
  const [selectedPaymentMatch, setSelectedPaymentMatch] =
    useState<PaymentMatch | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showPaymentProofModal, setShowPaymentProofModal] = useState(false);
  const [matchProofFile, setMatchProofFile] = useState<File | null>(null);
  const [matchActionLoading, setMatchActionLoading] = useState(false);



  const getStatusColor = (status: string | null | undefined) => {
    if (!status) return 'bg-muted/20 text-muted-foreground border-muted/30';

    switch (status) {
      case 'pending':
        return 'bg-warning/20 text-warning border-warning/30';
      case 'matched':
        return 'bg-primary/20 text-primary border-primary/30';
      case 'completed':
        return 'bg-success/20 text-success border-success/30';
      case 'cancelled':
        return 'bg-destructive/20 text-destructive border-destructive/30';
      default:
        return 'bg-muted/20 text-muted-foreground border-muted/30';
    }
  };

  const getStatusIcon = (status: string | null | undefined) => {
    if (!status) return null;

    switch (status) {
      case 'pending':
        return <Clock className='h-4 w-4' />;
      case 'matched':
        return <AlertCircle className='h-4 w-4' />;
      case 'completed':
        return <CheckCircle className='h-4 w-4' />;
      case 'cancelled':
        return <XCircle className='h-4 w-4' />;
      default:
        return null;
    }
  };

  const handleCancelFund = async () => {
    setShowCancelDialog(true);
  };

  const handleConfirmCancel = async () => {
    setShowCancelDialog(false);
    await cancelFund();
  };

  const handleCloseCancelDialog = () => {
    setShowCancelDialog(false);
  };

  const handleConfirmWithdraw = async () => {
    if (!fund) {
      toast.error('Fund information is missing');
      return;
    }

    const loadingToast = toast.loading('Processing withdrawal...');

    try {
      const response = await withdrawService.createWithdraw({
        fund_id: fund.id,
      });

      if (response.status === 'success') {
        toast.success('Withdrawal request created successfully! 🎉');
        setShowWithdrawModal(false);
        refreshFund();
      } else {
        toast.error(response.message || 'Failed to create withdrawal request');
      }
    } catch (error) {
      console.error('Error creating withdrawal:', error);
      toast.error('Failed to process withdrawal. Please try again.');
    } finally {
      toast.dismiss(loadingToast);
    }
  };

  // Payment match handlers
  const handlePaymentMatchAction = (
    match: PaymentMatch,
    action: 'pay' | 'proof'
  ) => {
    setSelectedPaymentMatch(match);
    if (action === 'pay') {
      setShowPaymentModal(true);
    } else {
      setShowPaymentProofModal(true);
    }
  };

  const handleConfirmPaymentSent = async (transactionHash: string) => {
    if (!selectedPaymentMatch) {
      toast.error('No payment match selected');
      return;
    }

    try {
      setMatchActionLoading(true);
      const response = await paymentMatchService.confirmPaymentSent(
        selectedPaymentMatch.id,
        { transaction_hash: transactionHash }
      );

      if (response.status === 'success') {
        toast.success('Payment confirmation sent successfully!');
        setShowPaymentModal(false);
        setSelectedPaymentMatch(null);
        await refreshFund();
      } else {
        toast.error(response.message || 'Failed to confirm payment');
      }
    } catch (error) {
      console.error('Payment confirmation error:', error);
      toast.error('An error occurred while confirming payment');
    } finally {
      setMatchActionLoading(false);
    }
  };

  const handleUploadMatchProof = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPaymentMatch || !matchProofFile) {
      toast.error('Please select a file');
      return;
    }

    try {
      setMatchActionLoading(true);
      const response = await paymentMatchService.uploadPaymentProof(
        selectedPaymentMatch.id,
        matchProofFile
      );

      if (response.status === 'success') {
        toast.success('Payment proof uploaded successfully!');
        setShowPaymentProofModal(false);
        setMatchProofFile(null);
        setSelectedPaymentMatch(null);
        await refreshFund();
      } else {
        toast.error(response.message || 'Failed to upload payment proof');
      }
    } catch (error) {
      console.error('Payment proof upload error:', error);
      toast.error('An error occurred while uploading payment proof');
    } finally {
      setMatchActionLoading(false);
    }
  };

  // Paystack admin payment handler
  const handlePaystackAdminPayment = async (match: PaymentMatch) => {
    if (!user?.email) {
      toast.error('User email not available');
      return;
    }

    if (!fund) {
      toast.error('Fund information not available');
      return;
    }

    try {
      await initiatePaystackPayment(match, fund, user.email);
    } catch (error) {
      console.error('Paystack payment error:', error);
      toast.error('Failed to initiate payment. Please try again.');
    }
  };

  const handleDisputeCreated = (_dispute: any) => {
    toast.success('Dispute details viewed successfully!');
    // You might want to refetch the fund data here to show updated dispute status
  };

  const getPaymentMatchStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-warning/20 text-warning border-warning/30',
      paid: 'bg-primary/20 text-primary border-primary/30',
      confirmed: 'bg-success/20 text-success border-success/30',
      completed: 'bg-success/20 text-success border-success/30',
      disputed: 'bg-destructive/20 text-destructive border-destructive/30',
    };
    return (
      variants[status as keyof typeof variants] ||
      'bg-muted/20 text-muted-foreground border-muted/30'
    );
  };

  if (loading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  if (!fund) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='text-center py-12'>
          <h2 className='text-xl font-semibold text-foreground mb-2'>
            Fund Not Found
          </h2>
          <p className='text-foreground-secondary mb-4'>
            The fund you're looking for doesn't exist or you don't have
            permission to view it.
          </p>
          <Link to='/funds'>
            <Button>Back to Funds</Button>
          </Link>
        </div>
      </div>
    );
  }

  const isMatured = new Date(fund.maturity_date) <= new Date();
  const canCancel = fund.status === 'pending';
  const canWithdraw = isMatured && fund.status === 'completed';
  const hasWithdrawRequest = fund.withdraws && fund.withdraws.length > 0;
  const fundWithdrawData = hasWithdrawRequest ? fund.withdraws![0] : null;

  return (
    <div className='container mx-auto px-6 py-8 max-w-4xl'>
      {/* Header with back button */}
      <div className='mb-8'>
        <Link
          to='/funds'
          className='inline-flex items-center text-foreground-secondary hover:text-foreground mb-6'
        >
          <ArrowLeft className='h-4 w-4 mr-2' />
          Back to funds
        </Link>

        <div className='flex items-center justify-between gap-4'>
          <div>
            <h1 className='text-3xl font-bold text-foreground'>Fund Details</h1>
            <p className='text-foreground-secondary mt-2'>
              View and manage your fund
            </p>
          </div>
          <Badge
            variant='outline'
            className={`${getStatusColor(fund.status)} flex items-center gap-1 px-3 py-1 text-sm flex-shrink-0`}
          >
            {getStatusIcon(fund.status)}
            {fund.status
              ? fund.status.charAt(0).toUpperCase() + fund.status.slice(1)
              : 'Unknown'}
          </Badge>
        </div>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Main Details */}
        <div className='lg:col-span-2 space-y-6'>
          {/* Fund Overview */}
          <div className='bg-background-secondary rounded-xl border border-border p-6'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>
              Fund Overview
            </h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div className='flex items-center gap-3'>
                  <Wallet className='h-5 w-5 text-primary' />
                  <div>
                    <p className='text-sm text-foreground-secondary'>Amount</p>
                    <p className='text-xl font-bold text-foreground'>
                      {formatCurrencyAmount(fund.amount, fund.currency)}
                    </p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <TrendingUp className='h-5 w-5 text-success' />
                  <div>
                    <p className='text-sm text-foreground-secondary'>
                      Expected Growth
                    </p>
                    <p className='text-lg font-semibold text-success'>
                      {formatCurrencyAmount(fund.growth_amount, fund.currency)}
                    </p>{' '}
                    <p className='text-xs text-foreground-secondary'>
                      {toNumber(fund.growth_percentage).toFixed(1)}% rate
                    </p>
                  </div>
                </div>
              </div>

              <div className='space-y-4'>
                <div className='flex items-center gap-3'>
                  <Calendar className='h-5 w-5 text-primary' />
                  <div>
                    <p className='text-sm text-foreground-secondary'>
                      Start Date
                    </p>
                    <p className='font-medium text-foreground'>
                      {formatDateTime(fund.start_date)}
                    </p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <Clock className='h-5 w-5 text-accent-foreground' />
                  <div>
                    <p className='text-sm text-foreground-secondary'>
                      Maturity Date
                    </p>
                    <p
                      className={`font-medium ${isMatured ? 'text-success' : 'text-foreground'}`}
                    >
                      {formatDateTime(fund.maturity_date)}
                    </p>
                    <p className='text-xs text-foreground-secondary'>
                      {fund.maturity_days} days duration
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Method */}
          <div className='bg-background-secondary rounded-xl border border-border p-6'>
            <h2 className='text-xl font-semibold text-foreground mb-4'>
              Payment Method
            </h2>
            {fund.payment_method ? (
              <div className='flex items-center gap-4 p-4 bg-background-tertiary rounded-lg'>
                <div className='w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center'>
                  <FileText className='h-5 w-5 text-primary' />
                </div>
                <div>
                  {fund.payment_method.type === 'bank' ? (
                    <>
                      <p className='font-medium text-foreground'>
                        {fund.payment_method.bank_name}
                      </p>
                      <p className='text-sm text-foreground-secondary'>
                        {fund.payment_method.account_number} -{' '}
                        {fund.payment_method.account_name}
                      </p>
                    </>
                  ) : (
                    <>
                      <p className='font-medium text-foreground'>
                        Crypto Wallet
                      </p>
                      <p className='text-sm text-foreground-secondary font-mono break-all'>
                        {fund.payment_method.wallet_address}
                      </p>
                    </>
                  )}
                </div>
              </div>
            ) : (
              <p className='text-foreground-secondary'>
                Payment method not available
              </p>
            )}
          </div>

          {/* Payment Matches */}
          <div className='bg-background-secondary rounded-xl border border-border p-6'>
            <div className='mb-4'>
              <h2 className='text-xl font-semibold text-foreground'>
                Payment Matches ({fund.payment_matches?.length || 0})
              </h2>
              <p className='text-sm text-foreground-secondary mt-1'>
                You will pay withdrawers
              </p>
            </div>

            {/* Countdown Components */}
            <div className='mb-6 space-y-4'>
              {/* Next Matching Countdown - Show for pending funds that aren't fully matched */}
              <NextMatchingCountdown
                shouldDisplay={
                  fund.status === 'pending' &&
                  fund.amount_matched < fund.amount
                }
              />

              {/* Payment Match Timeout Countdown - Show for matched funds with pending payment matches */}
              <PaymentMatchTimeoutCountdown
                fundId={fund.id}
                shouldDisplay={
                  fund.status === 'matched' &&
                  fund.payment_matches !== undefined &&
                  fund.payment_matches.some(match => match.status === 'pending')
                }
              />
            </div>
            {fund.payment_matches && fund.payment_matches.length > 0 ? (
              <div className='space-y-4'>
                {fund.payment_matches.map((match, index) => (
                  <FundPaymentMatchCard
                    key={match.id}
                    paymentMatch={match}
                    currency={fund.currency}

                    onPaymentMatchAction={handlePaymentMatchAction}
                    onPaystackAdminPayment={handlePaystackAdminPayment}
                    fund={fund}
                    index={index + 1}
                    onDisputeCreated={handleDisputeCreated}
                  />
                ))}
              </div>
            ) : (
              <div className='bg-primary/5 border border-primary/20 rounded-lg p-4'>
                <div className='text-center'>
                  <h3 className='text-lg font-medium text-primary mb-2'>
                    No payment matches yet
                  </h3>
                  {fund.status === 'pending' && (
                    <p className='text-foreground-secondary text-sm'>
                      Your fund is waiting to be matched with available
                      withdrawals.
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Actions Sidebar */}
        <div className='space-y-6'>
          {/* Quick Stats */}
          <div className='bg-background-secondary rounded-xl border border-border p-6'>
            <h3 className='font-semibold text-foreground mb-4'>Fund Stats</h3>
            <div className='space-y-3'>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary'>Fund Type</span>
                <span className='font-medium text-foreground'>
                  {fund.currency === 'fiat' ? 'Nigerian Naira' : 'Solana'}
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary'>
                  Maturity Period
                </span>
                <span className='font-medium text-foreground'>
                  {fund.maturity_days} days
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary'>
                  Days Remaining
                </span>
                <span className='font-medium text-foreground'>
                  {Math.max(
                    0,
                    Math.ceil(
                      (new Date(fund.maturity_date).getTime() -
                        new Date().getTime()) /
                        (1000 * 60 * 60 * 24)
                    )
                  )}{' '}
                  days
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary'>Growth Rate</span>
                <span className='font-medium text-success'>
                  {toNumber(fund.growth_percentage).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className='bg-background-secondary rounded-xl border border-border p-6'>
            <h3 className='font-semibold text-foreground mb-4'>Actions</h3>
            <div className='space-y-3'>
              {canCancel && (
                <Button
                  variant='destructive'
                  className='w-full'
                  onClick={handleCancelFund}
                  disabled={actionLoading}
                >
                  {actionLoading ? (
                    <LoadingSpinner size='sm' className='mr-2' />
                  ) : null}
                  Cancel Fund
                </Button>
              )}

              {canWithdraw && !hasWithdrawRequest && (
                <Button
                  className='w-full'
                  onClick={() => setShowWithdrawModal(true)}
                  //@ts-ignore
                  disabled={actionLoading || (user && !user.is_active)}
                >
                  {actionLoading ? (
                    <LoadingSpinner size='sm' className='mr-2' />
                  ) : null}
                  <ArrowDownToLine className='h-4 w-4 mr-2' />
                  Request Withdrawal
                </Button>
              )}

              {hasWithdrawRequest && fundWithdrawData && (
                <div className='bg-background-tertiary rounded-lg p-4 border border-border'>
                  <div className='flex items-center justify-between mb-3'>
                    <div className='flex items-center gap-2'>
                      <CheckCircle className='h-4 w-4 text-success' />
                      <span className='text-sm font-medium text-foreground'>
                        Withdrawal Requested
                      </span>
                    </div>
                    <Badge
                      variant='outline'
                      className={`${getStatusColor(fundWithdrawData.status)} text-xs`}
                    >
                      {fundWithdrawData.status}
                    </Badge>
                  </div>
                  <Link to={`/withdraws/${fundWithdrawData.id}`}>
                    <Button variant='outline' size='sm' className='w-full'>
                      View Withdrawal Details
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Withdraw Modal */}
      {showWithdrawModal && (
        <div className='fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50'>
          <div className='bg-background-secondary rounded-lg p-6 w-full max-w-md border border-border'>
            {/* Deactivated User Alert */}
            {user && !user.is_active && (
              <div className='mb-4'>
                <DeactivatedUserAlert
                  user={user}
                  customMessage='Your account has been deactivated. You cannot create withdrawal requests while your account is inactive.'
                  className='text-sm'
                />
              </div>
            )}

            {fund.next_fund_id && fund.next_fund?.status === 'completed' ? (
              // User has next fund with completed status - allow withdrawal
              <>
                <div className='flex items-center gap-3 mb-4'>
                  <div className='w-12 h-12 bg-success/10 rounded-full flex items-center justify-center'>
                    <CheckCircle className='h-6 w-6 text-success' />
                  </div>
                  <div>
                    <h3 className='text-lg font-semibold text-foreground'>
                      Withdraw Funds
                    </h3>
                    <p className='text-sm text-foreground-secondary'>
                      Your funds are ready for withdrawal
                    </p>
                  </div>
                </div>
                <div className='mb-6 p-4 bg-success/10 rounded-lg border border-success/20'>
                  <div className='flex justify-between items-center mb-2'>
                    <span className='text-sm text-foreground-secondary'>
                      Total Amount
                    </span>
                    <span className='font-semibold text-success'>
                      {formatCurrencyAmount(
                        Number(fund.amount) + Number(fund.growth_amount),
                        fund.currency
                      )}
                    </span>
                  </div>
                  <div className='flex justify-between items-center text-xs text-foreground-secondary'>
                    <span>
                      Principal: {formatCurrencyAmount(fund.amount, fund.currency)}
                    </span>
                    <span>
                      Growth: {formatCurrencyAmount(fund.growth_amount, fund.currency)}
                    </span>
                  </div>
                </div>
                <p className='text-sm text-foreground-secondary mb-4'>
                  Since you have created a recommitment fund, you can now
                  withdraw your matured funds. The withdrawal will be processed
                  to your payment method.
                </p>

                {/* Phone Number Emphasis */}
                <div className='bg-warning/10 border border-warning/30 rounded-lg p-3 mb-6'>
                  <div className='flex items-start gap-2'>
                    <AlertCircle className='h-4 w-4 text-warning flex-shrink-0 mt-0.5' />
                    <div>
                      <p className='text-xs text-foreground-secondary'>
                        <span className='font-medium text-warning'>
                          Keep your phone active & reachable:
                        </span>{' '}
                        Funders may need to contact you during the payment
                        process. Being unreachable could lead to payment delays
                        or account deactivation.
                      </p>
                    </div>
                  </div>
                </div>
                <div className='flex gap-3'>
                  <Button
                    type='button'
                    variant='outline'
                    className='flex-1'
                    onClick={() => setShowWithdrawModal(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    className='flex-1'
                    onClick={handleConfirmWithdraw}
                    //@ts-ignore
                    disabled={actionLoading || (user && !user.is_active)}
                  >
                    {actionLoading ? (
                      <LoadingSpinner size='sm' className='mr-2' />
                    ) : null}
                    Confirm Withdrawal
                  </Button>
                </div>
              </>
            ) : fund.next_fund_id && fund.next_fund?.status !== 'completed' ? (
              // User has next fund but it's not completed yet
              <>
                <div className='flex items-center gap-3 mb-4'>
                  <div className='w-12 h-12 bg-warning/10 rounded-full flex items-center justify-center'>
                    <AlertCircle className='h-6 w-6 text-warning' />
                  </div>
                  <div>
                    <h3 className='text-lg font-semibold text-foreground'>
                      Recommitment Not Ready
                    </h3>
                    <p className='text-sm text-foreground-secondary'>
                      Your recommitment fund must be completed first
                    </p>
                  </div>
                </div>
                <div className='mb-6 p-4 bg-warning/10 rounded-lg border border-warning/20'>
                  <p className='text-sm text-warning font-medium mb-3'>
                    To withdraw your matured funds, your recommitment fund must
                    have "completed" status.
                  </p>
                  <div className='space-y-2'>
                    <div className='flex items-center gap-2'>
                      <div className='w-1.5 h-1.5 bg-warning rounded-full flex-shrink-0 mt-1'></div>
                      <p className='text-xs text-foreground-secondary'>
                        Your recommitment fund current status:{' '}
                        <span className='font-semibold text-warning capitalize'>
                          {fund.next_fund?.status || 'Unknown'}
                        </span>
                      </p>
                    </div>
                    <div className='flex items-center gap-2'>
                      <div className='w-1.5 h-1.5 bg-warning rounded-full flex-shrink-0 mt-1'></div>
                      <p className='text-xs text-foreground-secondary'>
                        Complete the payment process for your recommitment fund
                        to enable withdrawal
                      </p>
                    </div>
                  </div>
                </div>
                <div className='flex gap-3'>
                  <Button
                    type='button'
                    variant='outline'
                    className='flex-1'
                    onClick={() => setShowWithdrawModal(false)}
                  >
                    Close
                  </Button>
                  <Button
                    className='flex-1'
                    onClick={() => {
                      setShowWithdrawModal(false);
                      window.location.href = `/funds/${fund.next_fund_id}`;
                    }}
                  >
                    View Recommitment
                  </Button>
                </div>
              </>
            ) : (
              // User needs to create next fund first
              <>
                <div className='flex items-center gap-3 mb-4'>
                  <div className='w-12 h-12 bg-warning/10 rounded-full flex items-center justify-center'>
                    <AlertCircle className='h-6 w-6 text-warning' />
                  </div>
                  <div>
                    <h3 className='text-lg font-semibold text-foreground'>
                      Create Recommitment Fund Required
                    </h3>
                    <p className='text-sm text-foreground-secondary'>
                      Complete your investment journey
                    </p>
                  </div>
                </div>
                <div className='mb-6 p-4 bg-warning/10 rounded-lg border border-warning/20'>
                  <p className='text-sm text-warning font-medium mb-3'>
                    To withdraw your matured funds, you must create your
                    recommitment fund first.
                  </p>
                  <div className='space-y-2'>
                    <div className='flex items-center gap-2'>
                      <div className='w-1.5 h-1.5 bg-warning rounded-full flex-shrink-0 mt-1'></div>
                      <p className='text-xs text-foreground-secondary'>
                        Your recommitment fund amount must be{' '}
                        <span className='font-semibold text-warning'>
                          greater than or equal to{' '}
                          {formatCurrencyAmount(fund.amount, fund.currency)}
                        </span>
                      </p>
                    </div>
                    <div className='flex items-center gap-2'>
                      <div className='w-1.5 h-1.5 bg-warning rounded-full flex-shrink-0 mt-1'></div>
                      <p className='text-xs text-foreground-secondary'>
                        This ensures continuous growth and maintains your
                        investment momentum
                      </p>
                    </div>
                  </div>
                </div>
                <div className='flex gap-3'>
                  <Button
                    type='button'
                    variant='outline'
                    className='flex-1'
                    onClick={() => setShowWithdrawModal(false)}
                  >
                    Cancel
                  </Button>
                  <Link to='/funds/create' className='flex-1'>
                    <Button className='w-full'>Create Recommitment</Button>
                  </Link>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Payment Modal */}
      {showPaymentModal && selectedPaymentMatch && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => {
            setShowPaymentModal(false);
            setSelectedPaymentMatch(null);
          }}
          onConfirm={handleConfirmPaymentSent}
          paymentMatch={selectedPaymentMatch}
          fundCurrency={fund?.currency || 'fiat'}

          loading={matchActionLoading}
        />
      )}

      {/* Payment Proof Upload Modal */}
      {showPaymentProofModal && selectedPaymentMatch && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
          <div className='bg-background rounded-lg max-w-md w-full p-6'>
            <div className='flex items-center justify-between mb-4'>
              <h2 className='text-lg font-semibold text-foreground'>
                Upload Payment Proof
              </h2>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setShowPaymentProofModal(false)}
              >
                <X className='h-4 w-4' />
              </Button>
            </div>

            <form onSubmit={handleUploadMatchProof} className='space-y-4'>
              <div className='bg-background-secondary rounded-lg p-4 mb-4'>
                <div className='flex items-center gap-3 mb-2'>
                  <Upload className='h-5 w-5 text-primary' />
                  <span className='font-medium text-foreground'>
                    Match Details
                  </span>
                </div>
                <div className='text-sm space-y-1'>
                  <div className='flex justify-between'>
                    <span className='text-foreground-secondary'>Amount:</span>
                    <span className='font-medium text-foreground'>
                      {formatCurrencyAmount(
                        selectedPaymentMatch.amount ||
                          selectedPaymentMatch.amount ||
                          0,
                        fund?.currency || 'fiat'
                      )}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-foreground-secondary'>Status:</span>
                    <Badge
                      className={getPaymentMatchStatusBadge(
                        selectedPaymentMatch.status
                      )}
                    >
                      {selectedPaymentMatch.status}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <label className='block text-sm font-medium text-foreground mb-2'>
                  Payment Proof Image
                </label>
                <div className='space-y-2'>
                  <input
                    type='file'
                    accept='image/*'
                    onChange={e =>
                      setMatchProofFile(e.target.files?.[0] || null)
                    }
                    className='hidden'
                    id='payment-proof-upload'
                  />
                  <label
                    htmlFor='payment-proof-upload'
                    className='flex items-center justify-center gap-2 w-full p-2 border-2 border-dashed border-border hover:border-primary/50 rounded-lg cursor-pointer transition-colors bg-background-secondary hover:bg-background-tertiary'
                  >
                    <Upload className='h-4 w-4 text-primary' />
                    <span className='text-foreground'>
                      {matchProofFile
                        ? matchProofFile.name
                        : 'Choose image file'}
                    </span>
                  </label>
                </div>
                <p className='text-xs text-foreground-secondary mt-1'>
                  Upload a screenshot or photo of your payment confirmation
                </p>
              </div>

              <div className='flex gap-3'>
                <Button
                  type='button'
                  variant='outline'
                  className='flex-1'
                  onClick={() => setShowPaymentProofModal(false)}
                  disabled={matchActionLoading}
                >
                  Cancel
                </Button>
                <Button
                  type='submit'
                  className='flex-1'
                  disabled={matchActionLoading || !matchProofFile}
                >
                  {matchActionLoading ? (
                    <LoadingSpinner size='sm' className='mr-2' />
                  ) : null}
                  Upload Proof
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Cancel Fund Dialog */}
      <CancelFundDialog
        open={showCancelDialog}
        fund={fund}
        onConfirm={handleConfirmCancel}
        onCancel={handleCloseCancelDialog}
      />
    </div>
  );
}
