import { apiClient } from './api';
import type {
  Fund,
  CreateFundData,
  FundStatistics,
  FundFilters,
  PaginatedFunds,
  ConfirmPaymentSentData,
  UploadPaymentProofData,
  ApiResponse,
} from '../types';

export const fundService = {
  /**
   * Get all funds for the authenticated user
   */
  getFunds: async (
    filters?: FundFilters
  ): Promise<ApiResponse<PaginatedFunds>> => {
    const params = new URLSearchParams();

    if (filters?.status) {
      params.append('status', filters.status);
    }
    if (filters?.currency) {
      params.append('currency', filters.currency);
    }
    if (filters?.sort_field) {
      params.append('sort_field', filters.sort_field);
    }
    if (filters?.sort_direction) {
      params.append('sort_direction', filters.sort_direction);
    }
    if (filters?.page) {
      params.append('page', filters.page.toString());
    }
    if (filters?.per_page) {
      params.append('per_page', filters.per_page.toString());
    }

    const queryString = params.toString();
    const url = queryString ? `/funds?${queryString}` : '/funds';

    return apiClient.get<PaginatedFunds>(url);
  },

  /**
   * Get a specific fund by ID
   */
  getFund: async (id: string): Promise<ApiResponse<Fund>> => {
    return apiClient.get<Fund>(`/funds/${id}`);
  },

  /**
   * Create a new fund
   */
  createFund: async (data: CreateFundData): Promise<ApiResponse<Fund>> => {
    return apiClient.post<Fund>('/funds', data);
  },

  /**
   * Cancel a fund
   */
  cancelFund: async (id: string): Promise<ApiResponse<Fund>> => {
    return apiClient.post<Fund>(`/funds/${id}/cancel`);
  },

  /**
   * Confirm payment sent for a matched fund
   */
  confirmPaymentSent: async (
    id: string,
    data: ConfirmPaymentSentData
  ): Promise<ApiResponse<Fund>> => {
    return apiClient.post<Fund>(`/funds/${id}/confirm-payment-sent`, data);
  },

  /**
   * Upload payment proof for a fund
   */
  uploadPaymentProof: async (
    id: string,
    data: UploadPaymentProofData
  ): Promise<ApiResponse<Fund>> => {
    const formData = new FormData();
    formData.append('payment_proof', data.payment_proof);

    return apiClient.post<Fund>(`/funds/${id}/upload-payment-proof`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Get fund statistics for the authenticated user
   */
  getFundStatistics: async (): Promise<ApiResponse<FundStatistics>> => {
    return apiClient.get<FundStatistics>('/funds/statistics');
  },

  /**
   * Get payment match timeout information for a specific fund
   */
  getTimeoutInfo: async (fundId: string): Promise<ApiResponse<any>> => {
    return apiClient.get<any>(`/funds/${fundId}/timeout-info`);
  },
};
