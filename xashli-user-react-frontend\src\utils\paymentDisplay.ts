/**
 * Utility functions for determining payment display logic
 * Provides deterministic behavior for showing Paystack vs actual payment method details
 */

/**
 * Generate a deterministic hash from a string
 * Uses a simple hash function for consistent results
 */
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Determine whether to show Paystack popup or actual payment method details
 * for admin withdrawals. Uses payment match ID to ensure consistent behavior.
 * 
 * @param paymentMatchId - The unique ID of the payment match
 * @returns true if should show Paystack popup, false if should show actual payment method
 */
export function shouldShowPaystackForAdminWithdraw(paymentMatchId: string): boolean {
  // Generate deterministic number from payment match ID
  const hash = simpleHash(paymentMatchId);
  
  // Use modulo to get 50/50 split (even = Paystack, odd = actual method)
  return hash % 2 === 0;
}

/**
 * Get display mode for admin withdrawal payment
 * 
 * @param paymentMatchId - The unique ID of the payment match
 * @returns 'paystack' or 'actual_method'
 */
export function getAdminWithdrawDisplayMode(paymentMatchId: string): 'paystack' | 'actual_method' {
  return shouldShowPaystackForAdminWithdraw(paymentMatchId) ? 'paystack' : 'actual_method';
}

/**
 * Check if a payment match should use Paystack display
 * Considers both admin withdraw status and currency type
 * 
 * @param isAdminWithdraw - Whether this is an admin withdrawal
 * @param currency - The currency type ('fiat' or 'crypto')
 * @param paymentMatchId - The unique ID of the payment match
 * @returns true if should use Paystack, false otherwise
 */
export function shouldUsePaystackDisplay(
  isAdminWithdraw: boolean, 
  currency: string, 
  paymentMatchId: string
): boolean {
  // Only consider Paystack for admin withdrawals with fiat currency
  if (!isAdminWithdraw || currency !== 'fiat') {
    return false;
  }
  
  // Use deterministic logic to decide
  return shouldShowPaystackForAdminWithdraw(paymentMatchId);
}

/**
 * Get payment instruction text based on display mode
 * 
 * @param displayMode - 'paystack' or 'actual_method'
 * @returns instruction text for the user
 */
export function getPaymentInstructionText(displayMode: 'paystack' | 'actual_method'): string {
  if (displayMode === 'paystack') {
    return 'Click "Send Payment" to pay via Paystack (Bank Transfer)';
  } else {
    return 'Use the payment method details below to send payment manually';
  }
}

/**
 * Get button text based on display mode
 * 
 * @param displayMode - 'paystack' or 'actual_method'
 * @returns button text
 */
export function getPaymentButtonText(displayMode: 'paystack' | 'actual_method'): string {
  if (displayMode === 'paystack') {
    return 'Pay via Paystack';
  } else {
    return 'Send Payment';
  }
}
