/**
 * Utility functions for determining payment display logic
 * Provides deterministic behavior for showing Paystack vs payment modal
 */

/**
 * Generate a deterministic hash from a string
 * Uses a simple hash function for consistent results
 */
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Check if a payment match should use Paystack display
 * Considers both admin withdraw status and currency type
 *
 * @param isAdminWithdraw - Whether this is an admin withdrawal
 * @param currency - The currency type ('fiat' or 'crypto')
 * @param paymentMatchId - The unique ID of the payment match
 * @returns true if should use Paystack popup, false if should show payment modal
 */
export function shouldUsePaystackDisplay(
  isAdminWithdraw: boolean,
  currency: string,
  paymentMatchId: string
): boolean {
  // Only consider Paystack for admin withdrawals with fiat currency
  if (!isAdminWithdraw || currency !== 'fiat') {
    return false;
  }

  // Generate deterministic number from payment match ID
  const hash = simpleHash(paymentMatchId);

  // Use modulo to get 50/50 split (even = Paystack, odd = payment modal)
  return hash % 2 === 0;
}
